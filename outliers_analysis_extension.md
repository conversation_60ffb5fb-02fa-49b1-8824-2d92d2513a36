# Расширенный анализ выбросов по городам

## Обзор реализации

Расширил анализ выбросов в файле `taxi_analysis_modular_refactored.ipynb` следующим образом:

## 1. Новые функции

### `detect_outliers_by_groups()`
- **Назначение**: Ана<PERSON>из выбросов с группировкой по заданному столбцу (например, по городам)
- **Параметры**:
  - `df`: DataFrame с данными
  - `metrics`: Список метрик для анализа (если None, анализируются все числовые столбцы)
  - `group_by`: Столбец для группировки (по умолчанию 'city')
  - `method`: Метод детекции ('iqr')
  - `threshold`: Пороговое значение для IQR (по умолчанию 1.5)
- **Возвращает**: Словарь с результатами анализа и сводной таблицей

### `visualize_outliers_by_groups()`
- **Назначение**: Визуализация результатов анализа выбросов
- **Создает**: 4 графика в одной фигуре:
  1. Тепловая карта количества выбросов
  2. Тепловая карта процента выбросов
  3. Столбчатая диаграмма по группам (городам)
  4. Столбчатая диаграмма по метрикам

### `print_outliers_summary_table()`
- **Назначение**: Вывод детальной сводной таблицы результатов
- **Включает**:
  - Таблицу с результатами по каждой метрике и группе
  - Общую статистику
  - Статистику по группам (городам)
  - Статистику по метрикам

## 2. Применение к множественным метрикам

Анализ настроен для работы с двумя типами метрик:

### Метрики количества:
- `cnt_order` - количество заказов
- `cnt_offer` - количество предложений
- `cnt_assign` - количество назначений
- `cnt_arrive` - количество прибытий
- `cnt_trip` - количество поездок

### Метрики конверсии:
- `order2trip` - конверсия заказ → поездка
- `order2offer` - конверсия заказ → предложение
- `offer2assign` - конверсия предложение → назначение
- `assign2arrive` - конверсия назначение → прибытие
- `arrive2trip` - конверсия прибытие → поездка

## 3. Группировка по городам

- Выбросы определяются **относительно каждого города отдельно**
- Это важно, поскольку нормальные значения для крупного города могут быть выбросами для маленького
- Анализ проводится для каждой комбинации "метрика × город"

## 4. Использование IQR метода

- Применяется метод межквартильного размаха (IQR)
- Порог по умолчанию: 1.5 (стандартное значение)
- Выбросы: значения < Q1 - 1.5×IQR или > Q3 + 1.5×IQR

## 5. Структура реализации

### Секция 5.7: Расширенный анализ выбросов по городам
1. **Анализ метрик количества** - отдельный анализ для cnt_* метрик
2. **Визуализация метрик количества** - графики и тепловые карты
3. **Анализ метрик конверсии** - отдельный анализ для конверсий
4. **Визуализация метрик конверсии** - графики и тепловые карты
5. **Комплексный анализ** - анализ всех метрик вместе
6. **Детальный анализ по городам** - топ-3 метрики с выбросами для каждого города

### Секция 6: Демонстрация
- Практический пример запуска анализа на реальных данных
- Обработка ошибок и валидация данных

## 6. Результаты анализа

Анализ предоставляет:

### Сводные таблицы:
- Количество и процент выбросов по каждой метрике и городу
- Общая статистика по всем данным
- Ранжированные списки городов и метрик по количеству выбросов

### Визуализация:
- **Тепловые карты** - показывают распределение выбросов по городам и метрикам
- **Столбчатые диаграммы** - сравнение городов и метрик по количеству выбросов
- **Цветовое кодирование** - для лучшего восприятия данных

### Детальная статистика:
- Границы выбросов (нижняя и верхняя) для каждой группы
- Процентное соотношение выбросов к общему количеству записей
- Топ-проблемные метрики для каждого города

## 7. Преимущества реализации

1. **Модульность** - функции можно использовать независимо
2. **Гибкость** - настраиваемые параметры для разных типов анализа
3. **Масштабируемость** - легко добавить новые метрики или изменить группировку
4. **Визуализация** - наглядное представление результатов
5. **Автоматизация** - минимум ручной работы для получения полного анализа

## 8. Использование

```python
# Базовый анализ выбросов по городам
outliers = detect_outliers_by_groups(
    df_metrics, 
    metrics=['cnt_order', 'cnt_offer'], 
    group_by='city'
)

# Вывод результатов
print_outliers_summary_table(outliers)

# Визуализация
visualize_outliers_by_groups(outliers)
```

Расширенный анализ выбросов теперь полностью интегрирован в существующий код и готов к использованию на реальных данных такси.
