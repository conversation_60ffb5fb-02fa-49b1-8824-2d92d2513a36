import warnings
from typing import Any

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

def load_data(file_path: str) -> pd.DataFrame:
    return pd.read_csv(file_path)


def validate_required_columns(df: pd.DataFrame, required_columns: list[str]) -> None:
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        msg = f"Отсутствуют обязательные колонки: {missing_columns}"
        raise ValueError(msg)


def check_duplicates(df: pd.DataFrame, id_column: str = "id_order") -> None:
    if id_column not in df.columns:
        return

    duplicates = df[id_column].duplicated().sum()

    if duplicates > 0:
        warnings.warn(f"Обнаружено {duplicates} дублированных ID", stacklevel=2)

def analyze_missing_values(df: pd.DataFrame, threshold: float = 50.0) -> None:
    """
    Анализирует пропущенные значения в DataFrame и выдает предупреждения
    для колонок с высоким процентом пропусков.
    """
    for column in df.columns:
        if column.endswith("_time"):
            continue

        missing_count = df[column].isna().sum()
        if missing_count > 0:
            missing_percentage = (missing_count / len(df)) * 100
            if missing_percentage > threshold:
                warnings.warn(f"Колонка {column} содержит более {threshold}% пропущенных значений", stacklevel=2)

def convert_time_column(series: pd.Series, time_format: str = "mixed") -> pd.Series:
    if series.dtype == "datetime64[ns]":
        return series

    return pd.to_datetime(series, format=time_format)


def convert_time_columns(df: pd.DataFrame, time_columns: list[str], time_format: str = "mixed") -> pd.DataFrame:
    df_result = df.copy()

    for col in time_columns:
        if col in df_result.columns:
            df_result[col] = convert_time_column(df_result[col], time_format)

    return df_result


def add_time_derived_columns(df: pd.DataFrame, base_column: str = "order_time") -> pd.DataFrame:
    df_result = df.copy()

    if base_column in df_result.columns:
        df_result["day_order"] = df_result[base_column].dt.day

    return df_result

def create_aggregation_config(df: pd.DataFrame) -> dict[str, tuple[str, str]]:
    agg_dict = {}

    column_mapping = {
        "cnt_order": "id_order",
        "cnt_offer": "offer_time",
        "cnt_assign": "assign_time",
        "cnt_arrive": "arrive_time",
        "cnt_trip": "trip_time",
    }

    for metric_name, column in column_mapping.items():
        if column in df.columns:
            agg_dict[metric_name] = (column, "count")

    return agg_dict


def group_and_aggregate(
    df: pd.DataFrame,
    group_by: str | list[str],
    agg_config: dict[str, tuple[str, str]],
) -> pd.DataFrame:
    agg_dict = {}
    for new_col_name, (source_col, agg_func) in agg_config.items():
        agg_dict[new_col_name] = (source_col, agg_func)

    return df.groupby(group_by, as_index=False).agg(**agg_dict)

def calculate_conversion(df: pd.DataFrame, numerator_col: str, denominator_col: str) -> pd.Series:
    if numerator_col not in df.columns:
        msg = f"Колонка {numerator_col} не найдена"
        raise ValueError(msg)
    if denominator_col not in df.columns:
        msg = f"Колонка {denominator_col} не найдена"
        raise ValueError(msg)

    return df[numerator_col] / df[denominator_col].replace(0, np.nan)


def calculate_all_conversions(df: pd.DataFrame) -> pd.DataFrame:
    df_result = df.copy()

    conversions = [
        ("cnt_trip", "cnt_order", "order2trip"),
        ("cnt_offer", "cnt_order", "order2offer"),
        ("cnt_assign", "cnt_offer", "offer2assign"),
        ("cnt_arrive", "cnt_assign", "assign2arrive"),
        ("cnt_trip", "cnt_arrive", "arrive2trip"),
    ]

    for num_col, den_col, conv_name in conversions:
        df_result[conv_name] = calculate_conversion(df_result, num_col, den_col)

    return df_result.replace([np.inf, -np.inf], np.nan)

def get_city_data(df: pd.DataFrame, cities: list[str] | None = None) -> tuple[list[str], list[pd.DataFrame]]:
    if "city" not in df.columns:
        msg = "Колонка 'city' не найдена в данных"
        raise ValueError(msg)

    available_cities = df["city"].unique().tolist()

    if cities is None:
        cities = available_cities
    else:
        missing_cities = [city for city in cities if city not in available_cities]
        if missing_cities:
            cities = [city for city in cities if city in available_cities]

    if not cities:
        msg = "Нет доступных городов для отображения"
        raise ValueError(msg)

    city_data_list = []
    for city in cities:
        city_data = df[df["city"] == city]
        city_data_list.append(city_data)

    return cities, city_data_list


def plot_city_lines(cities: list[str], city_data_list: list[pd.DataFrame], x_column: str, y_column: str) -> None:
    for city, city_data in zip(cities, city_data_list, strict=False):
        if len(city_data) == 0 or x_column not in city_data.columns or y_column not in city_data.columns:
            continue

        plt.plot(
            city_data[x_column],
            city_data[y_column],
            label=city,
            marker="o",
            linewidth=2,
            markersize=6,
        )


def setup_plot_labels(
    title: str | None,
    x_column: str,
    y_column: str,
    y_limit: tuple[float, float] | None = None,
) -> None:
    plt.legend(bbox_to_anchor=(1.05, 1), loc="upper left")
    plt.grid(visible=True, alpha=0.3)

    if title:
        plt.title(title, fontsize=14, fontweight="bold")
    else:
        plt.title(f"{y_column} по городам", fontsize=14, fontweight="bold")

    plt.xlabel(x_column.replace("_", " ").title(), fontsize=12)
    plt.ylabel(y_column.replace("_", " ").title(), fontsize=12)

    if y_limit:
        plt.ylim(y_limit)

    plt.tight_layout()


def plot_metric_by_cities(
    df: pd.DataFrame,
    metric_column: str,
    x_column: str = "day_order",
    cities: list[str] | None = None,
    title: str | None = None,
    y_limit: tuple[float, float] | None = None,
    figsize: tuple[int, int] = (12, 8),
    style: str = "seaborn-v0_8",
) -> None:
    required_cols = [metric_column, x_column]
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        msg = f"Отсутствуют колонки: {missing_cols}"
        raise ValueError(msg)

    plt.style.use(style)
    plt.figure(figsize=figsize)
    cities, city_data_list = get_city_data(df, cities)
    plot_city_lines(cities, city_data_list, x_column, metric_column)
    setup_plot_labels(title, x_column, metric_column, y_limit)

    plt.show()

def detect_outliers_iqr(data: pd.Series, threshold: float = 1.5) -> pd.Series:
    q1 = data.quantile(0.25)
    q3 = data.quantile(0.75)
    iqr = q3 - q1

    lower_bound = q1 - threshold * iqr
    upper_bound = q3 + threshold * iqr

    return (data < lower_bound) | (data > upper_bound)


def analyze_column_outliers(df: pd.DataFrame, column: str, threshold: float = 1.5) -> dict[str, Any]:
    """
    Анализирует выбросы в одной колонке методом IQR.
    """
    if column not in df.columns:
        msg = f"Колонка {column} не найдена"
        raise ValueError(msg)

    data = df[column].dropna()
    if len(data) == 0:
        return {"column": column, "outliers_count": 0, "outliers_percentage": 0.0}

    outlier_mask = detect_outliers_iqr(data, threshold)
    outliers_count = outlier_mask.sum()
    outliers_percentage = (outliers_count / len(data)) * 100

    return {
        "column": column,
        "outliers_count": outliers_count,
        "outliers_percentage": outliers_percentage,
        "threshold": threshold,
        "outlier_indices": data[outlier_mask].index.tolist(),
    }

def detect_outliers_by_groups(
    df: pd.DataFrame,
    metrics: list[str] | None = None,
    group_by: str = "city",
    method: str = "iqr",
    threshold: float = 1.5,
) -> dict[str, Any]:
    """
    Анализ выбросов с группировкой по заданному столбцу.

    Parameters
    ----------
    df : pd.DataFrame
        Данные для анализа
    metrics : list[str] | None
        Список метрик для анализа. Если None, анализируются все числовые столбцы
    group_by : str
        Столбец для группировки (например, 'city')
    method : str
        Метод детекции выбросов ('iqr')
    threshold : float
        Пороговое значение для IQR метода

    Returns
    -------
    dict[str, Any]
        Результаты анализа выбросов по группам

    """
    if group_by not in df.columns:
        msg = f"Столбец для группировки '{group_by}' не найден"
        raise ValueError(msg)

    if metrics is None:
        # Автоматический выбор числовых столбцов, исключая группировочный
        metrics = [col for col in df.select_dtypes(include=[np.number]).columns if col != group_by]
        print(f"Анализ выбросов для метрик: {metrics}")

    print(f"Группировка по: {group_by}")
    print(f"Метод: {method}, порог: {threshold}")

    results = {}
    summary_table = []

    groups = df[group_by].unique()
    print(f"Найдено групп: {len(groups)}")

    for metric in metrics:
        if metric not in df.columns:
            print(f"⚠ Метрика {metric} не найдена")
            continue

        metric_results = {}

        for group in groups:
            group_data = df[df[group_by] == group][metric].dropna()

            if len(group_data) == 0:
                continue

            # Применение IQR метода
            outlier_mask = detect_outliers_iqr(group_data, threshold)
            outlier_count = outlier_mask.sum()
            outlier_percent = (outlier_count / len(group_data)) * 100

            q1 = group_data.quantile(0.25)
            q3 = group_data.quantile(0.75)
            iqr = q3 - q1
            lower_bound = q1 - threshold * iqr
            upper_bound = q3 + threshold * iqr

            metric_results[group] = {
                "count": outlier_count,
                "percentage": outlier_percent,
                "total_records": len(group_data),
                "lower_bound": lower_bound,
                "upper_bound": upper_bound,
            }

            # Добавляем в сводную таблицу
            summary_table.append(
                {
                    "metric": metric,
                    "group": group,
                    "outliers_count": outlier_count,
                    "outliers_percentage": outlier_percent,
                    "total_records": len(group_data),
                }
            )

        results[metric] = metric_results

    return {
        "results": results,
        "summary_table": pd.DataFrame(summary_table),
        "method": method,
        "threshold": threshold,
        "group_by": group_by,
    }

def print_outliers_summary_table(outlier_results: dict[str, Any]) -> None:
    """
    Выводит сводную таблицу результатов анализа выбросов.

    Parameters
    ----------
    outlier_results : dict[str, Any]
        Результаты анализа выбросов от функции detect_outliers_by_groups

    """
    summary_df = outlier_results["summary_table"]
    group_by = outlier_results["group_by"]
    method = outlier_results["method"]
    threshold = outlier_results["threshold"]

    print("\n=== СВОДНАЯ ТАБЛИЦА ВЫБРОСОВ ===")
    print(f"Метод: {method.upper()}, порог: {threshold}")
    print(f"Группировка по: {group_by}\n")

    if len(summary_df) == 0:
        print("Нет данных для отображения")
        return

    # Создаем красивую таблицу
    display_df = summary_df.copy()
    display_df["outliers_percentage"] = display_df["outliers_percentage"].round(2)
    display_df = display_df.rename(
        columns={
            "metric": "Метрика",
            "group": group_by.capitalize(),
            "outliers_count": "Выбросы",
            "outliers_percentage": "Процент (%)",
            "total_records": "Всего записей",
        }
    )

    print(display_df.to_string(index=False))

    # Общая статистика
    total_outliers = summary_df["outliers_count"].sum()
    total_records = summary_df["total_records"].sum()
    overall_percent = (total_outliers / total_records * 100) if total_records > 0 else 0

    print("\n=== ОБЩАЯ СТАТИСТИКА ===")
    print(f"Всего выбросов: {total_outliers}")
    print(f"Всего записей: {total_records}")
    print(f"Общий процент выбросов: {overall_percent:.2f}%")

    # Статистика по группам
    print(f"\n=== СТАТИСТИКА ПО {group_by.upper()} ===")
    group_stats = (
        summary_df.groupby("group")
        .agg({"outliers_count": "sum", "total_records": "sum"})
        .assign(percentage=lambda x: (x["outliers_count"] / x["total_records"] * 100).round(2))
        .sort_values("outliers_count", ascending=False)
    )

    for group, row in group_stats.iterrows():
        print(f"{group}: {row['outliers_count']} выбросов ({row['percentage']:.2f}%) из {row['total_records']} записей")

    # Статистика по метрикам
    print("\n=== СТАТИСТИКА ПО МЕТРИКАМ ===")
    metric_stats = (
        summary_df.groupby("metric")
        .agg({"outliers_count": "sum", "total_records": "sum"})
        .assign(percentage=lambda x: (x["outliers_count"] / x["total_records"] * 100).round(2))
        .sort_values("outliers_count", ascending=False)
    )

    for metric, row in metric_stats.iterrows():
        print(
            f"{metric}: {row['outliers_count']} выбросов ({row['percentage']:.2f}%) из {row['total_records']} записей"
        )

def visualize_outliers_by_groups(outlier_results: dict[str, Any], figsize: tuple[int, int] = (15, 10)) -> None:
    """
    Визуализация результатов анализа выбросов по группам.

    Parameters
    ----------
    outlier_results : dict[str, Any]
        Результаты анализа выбросов от функции detect_outliers_by_groups
    figsize : tuple[int, int]
        Размер фигуры

    """
    summary_df = outlier_results["summary_table"]
    group_by = outlier_results["group_by"]

    if len(summary_df) == 0:
        print("Нет данных для визуализации")
        return

    # Создаем сводную таблицу для графиков
    pivot_count = summary_df.pivot(index="metric", columns="group", values="outliers_count")
    pivot_percent = summary_df.pivot(index="metric", columns="group", values="outliers_percentage")

    fig, axes = plt.subplots(2, 2, figsize=figsize)
    fig.suptitle(f"Анализ выбросов по {group_by}", fontsize=16, fontweight="bold")

    # 1. Тепловая карта количества выбросов (имитация seaborn)
    im1 = axes[0, 0].imshow(pivot_count.values, cmap="Reds", aspect="auto")
    axes[0, 0].set_title("Количество выбросов")
    axes[0, 0].set_xlabel(group_by.capitalize())
    axes[0, 0].set_ylabel("Метрики")
    axes[0, 0].set_xticks(range(len(pivot_count.columns)))
    axes[0, 0].set_xticklabels(pivot_count.columns, rotation=45)
    axes[0, 0].set_yticks(range(len(pivot_count.index)))
    axes[0, 0].set_yticklabels(pivot_count.index)

    # Добавляем значения на тепловую карту
    for i in range(len(pivot_count.index)):
        for j in range(len(pivot_count.columns)):
            value = pivot_count.iloc[i, j]
            if not pd.isna(value):
                axes[0, 0].text(j, i, f"{int(value)}", ha="center", va="center")

    # 2. Тепловая карта процента выбросов
    im2 = axes[0, 1].imshow(pivot_percent.values, cmap="Oranges", aspect="auto")
    axes[0, 1].set_title("Процент выбросов (%)")
    axes[0, 1].set_xlabel(group_by.capitalize())
    axes[0, 1].set_ylabel("Метрики")
    axes[0, 1].set_xticks(range(len(pivot_percent.columns)))
    axes[0, 1].set_xticklabels(pivot_percent.columns, rotation=45)
    axes[0, 1].set_yticks(range(len(pivot_percent.index)))
    axes[0, 1].set_yticklabels(pivot_percent.index)

    # Добавляем значения на тепловую карту
    for i in range(len(pivot_percent.index)):
        for j in range(len(pivot_percent.columns)):
            value = pivot_percent.iloc[i, j]
            if not pd.isna(value):
                axes[0, 1].text(j, i, f"{value:.1f}", ha="center", va="center")

    # 3. Столбчатая диаграмма общего количества выбросов по группам
    group_totals = summary_df.groupby("group")["outliers_count"].sum().sort_values(ascending=False)
    colors = plt.cm.Set3(np.linspace(0, 1, len(group_totals)))
    bars = axes[1, 0].bar(group_totals.index, group_totals.values, color=colors)
    axes[1, 0].set_title(f"Общее количество выбросов по {group_by}")
    axes[1, 0].set_xlabel(group_by.capitalize())
    axes[1, 0].set_ylabel("Количество выбросов")
    axes[1, 0].tick_params(axis="x", rotation=45)

    # Добавляем значения на столбцы
    for bar in bars:
        height = bar.get_height()
        axes[1, 0].text(bar.get_x() + bar.get_width() / 2.0, height, f"{int(height)}", ha="center", va="bottom")

    # 4. Столбчатая диаграмма по метрикам
    metric_totals = summary_df.groupby("metric")["outliers_count"].sum().sort_values(ascending=False)
    colors = plt.cm.Set2(np.linspace(0, 1, len(metric_totals)))
    bars = axes[1, 1].bar(metric_totals.index, metric_totals.values, color=colors)
    axes[1, 1].set_title("Общее количество выбросов по метрикам")
    axes[1, 1].set_xlabel("Метрики")
    axes[1, 1].set_ylabel("Количество выбросов")
    axes[1, 1].tick_params(axis="x", rotation=45)

    # Добавляем значения на столбцы
    for bar in bars:
        height = bar.get_height()
        axes[1, 1].text(bar.get_x() + bar.get_width() / 2.0, height, f"{int(height)}", ha="center", va="bottom")

    plt.tight_layout()
    plt.show()

def validate_visualization_columns(df: pd.DataFrame) -> None:
    required_columns = ["cnt_order", "order2trip", "order2offer", "offer2assign", "assign2arrive", "arrive2trip"]

    missing_columns = [col for col in required_columns if col not in df.columns]

    if missing_columns:
        msg = f"Отсутствуют колонки для визуализации: {missing_columns}"
        raise ValueError(msg)

def perform_complete_analysis(
    file_path: str,
    required_columns: list[str] | None = None,
    time_columns: list[str] | None = None,
    group_by: str | list[str] | None = None,
) -> tuple[pd.DataFrame, pd.DataFrame]:
    """
    Выполняет полный анализ данных такси, используя модульные функции.
    """
    if group_by is None:
        group_by = ["day_order", "city"]
    df = load_data(file_path)

    if required_columns is None:
        required_columns = [
            "id_order",
            "order_time",
            "offer_time",
            "assign_time",
            "arrive_time",
            "trip_time",
            "city",
        ]

    validate_required_columns(df, required_columns)
    check_duplicates(df)
    analyze_missing_values(df)

    if time_columns is None:
        time_columns = [
            "order_time",
            "offer_time",
            "assign_time",
            "arrive_time",
            "trip_time",
        ]

    df = convert_time_columns(df, time_columns)
    df = add_time_derived_columns(df)

    agg_config = create_aggregation_config(df)
    df_metrics = group_and_aggregate(df, group_by, agg_config)
    df_metrics = calculate_all_conversions(df_metrics)

    return df, df_metrics

# Выполняем полный анализ
df_original, df_metrics = perform_complete_analysis("taxi_data.csv")

validate_visualization_columns(df_metrics)


df_original.head()

df_metrics.head()

plot_metric_by_cities(df_metrics, "cnt_order", title="Количество заказов по городам")

plot_metric_by_cities(df_metrics, "order2trip", title="Конверсия заказ -> поездка", y_limit=(0, 1))

# График конверсии заказ -> предложение
plot_metric_by_cities(df_metrics, "order2offer", title="Конверсия заказ -> предложение", y_limit=(0, 1))

# График конверсии предложение -> назначение
plot_metric_by_cities(df_metrics, "offer2assign", title="Конверсия предложение -> назначение", y_limit=(0, 1))

# График конверсии назначение -> прибытие
plot_metric_by_cities(df_metrics, "assign2arrive", title="Конверсия назначение -> прибытие", y_limit=(0, 1))

# График конверсии прибытие -> поездка
plot_metric_by_cities(df_metrics, "arrive2trip", title="Конверсия прибытие -> поездка", y_limit=(0, 1))

# Анализ выбросов в количестве заказов
outliers_result = analyze_column_outliers(df_metrics, "cnt_order")
print(
    f"Выбросы в колонке cnt_order: {outliers_result['outliers_count']} ({outliers_result['outliers_percentage']:.2f}%)"
)

# Анализ выбросов в метриках количества по городам
print("=== АНАЛИЗ ВЫБРОСОВ В МЕТРИКАХ КОЛИЧЕСТВА ПО ГОРОДАМ ===")

# Определяем метрики количества для анализа
count_metrics = ["cnt_order", "cnt_offer", "cnt_assign", "cnt_arrive", "cnt_trip"]

# Выполняем анализ выбросов с группировкой по городам
outliers_by_city = detect_outliers_by_groups(
    df_metrics, metrics=count_metrics, group_by="city", method="iqr", threshold=1.5
)

# Выводим сводную таблицу результатов
print_outliers_summary_table(outliers_by_city)

# Визуализация результатов анализа выбросов
print("\nВизуализация выбросов по городам:")
visualize_outliers_by_groups(outliers_by_city, figsize=(16, 12))

# Анализ выбросов в метриках конверсии по городам
print("=== АНАЛИЗ ВЫБРОСОВ В МЕТРИКАХ КОНВЕРСИИ ПО ГОРОДАМ ===")
print("Метрики конверсии более подходят для детекции аномалий:")
print("- Стабильны во времени (в отличие от количественных метрик)")
print("- Резкие изменения указывают на проблемы в системе")
print("- Практически значимы для системы алертов\n")

# Определяем метрики конверсии для анализа
conversion_metrics = [
    "order2trip",  # Общая конверсия заказ → поездка
    "order2offer",  # Конверсия заказ → предложение
    "offer2assign",  # Конверсия предложение → назначение
    "assign2arrive",  # Конверсия назначение → прибытие
    "arrive2trip",  # Конверсия прибытие → поездка
]

print(f"Анализируемые метрики конверсии: {conversion_metrics}\n")

# Выполняем анализ выбросов с группировкой по городам (IQR метод, порог 1.5)
conversion_outliers = detect_outliers_by_groups(
    df_metrics, metrics=conversion_metrics, group_by="city", method="iqr", threshold=1.5
)

# Выводим сводную таблицу результатов
print_outliers_summary_table(conversion_outliers)

# Визуализация результатов анализа выбросов
print("\nВизуализация выбросов в конверсиях по городам:")
visualize_outliers_by_groups(conversion_outliers, figsize=(16, 12))

# Визуализация выбросов в метриках конверсии
print("\nВизуализация выбросов в метриках конверсии:")
visualize_outliers_by_groups(conversion_outliers, figsize=(16, 12))

# Комплексный анализ всех числовых метрик
print("\n3. Комплексный анализ всех числовых метрик:")
all_metrics = count_metrics + conversion_metrics

all_outliers = detect_outliers_by_groups(df_metrics, metrics=all_metrics, group_by="city", method="iqr", threshold=1.5)

# Выводим сводную таблицу
print_outliers_summary_table(all_outliers)

# Детальный анализ выбросов по каждому городу
print("\n=== ДЕТАЛЬНЫЙ АНАЛИЗ ПО ГОРОДАМ ===")

for city in df_metrics["city"].unique():
    print(f"\n--- {city} ---")
    city_data = df_metrics[df_metrics["city"] == city]

    # Анализ выбросов для данного города
    city_outliers = {}
    for metric in all_metrics:
        if metric in city_data.columns:
            result = analyze_column_outliers(city_data, metric, threshold=1.5)
            city_outliers[metric] = result

    # Выводим топ-3 метрики с наибольшим количеством выбросов
    sorted_metrics = sorted(city_outliers.items(), key=lambda x: x[1]["outliers_count"], reverse=True)[:3]

    print("Топ-3 метрики с выбросами:")
    for metric, details in sorted_metrics:
        if details["outliers_count"] > 0:
            print(f"  {metric}: {details['outliers_count']} выбросов ({details['outliers_percentage']:.1f}%)")

    if not any(details["outliers_count"] > 0 for _, details in sorted_metrics):
        print("  Выбросы не обнаружены")