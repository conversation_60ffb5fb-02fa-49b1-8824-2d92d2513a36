import pandas as pd
import matplotlib.pyplot as plt

df = pd.read_excel('taxi_data.xlsx')

df['trip_time'] = pd.to_datetime(df['trip_time'], format="%d.%m.%y %H:%M")
df['order_time'] = pd.to_datetime(df['order_time'], format="%d.%m.%y %H:%M")

df.head()

df['day_order'] = df['order_time'].dt.day
df['hour_order'] = df['order_time'].dt.floor('h')

df_gr_dyn = df.groupby('day_order', as_index = False).agg(cnt_order = ('id_order','count')
                                                    , cnt_offer = ('offer_time','count')
                                                    , cnt_assign = ('assign_time','count')
                                                    , cnt_arrive = ('arrive_time','count')
                                                    , cnt_trip = ('trip_time','count')
                                                    )
df_gr_dyn['order2trip']    = df_gr_dyn['cnt_trip']   / df_gr_dyn['cnt_order']
df_gr_dyn['order2offer']   = df_gr_dyn['cnt_offer']  / df_gr_dyn['cnt_order']
df_gr_dyn['offer2assign']  = df_gr_dyn['cnt_assign'] / df_gr_dyn['cnt_offer']
df_gr_dyn['assign2arrive'] = df_gr_dyn['cnt_arrive'] / df_gr_dyn['cnt_assign']
df_gr_dyn['arrive2trip']   = df_gr_dyn['cnt_trip']   / df_gr_dyn['cnt_arrive']
df_gr_dyn

df_gr_dyn_city = df.groupby(['day_order','city'], as_index = False).agg(cnt_order = ('id_order','count')
                                                    , cnt_offer = ('offer_time','count')
                                                    , cnt_assign = ('assign_time','count')
                                                    , cnt_arrive = ('arrive_time','count')
                                                    , cnt_trip = ('trip_time','count')
                                                    )
df_gr_dyn_city['order2trip']    = df_gr_dyn_city['cnt_trip']   / df_gr_dyn_city['cnt_order']
df_gr_dyn_city['order2offer']   = df_gr_dyn_city['cnt_offer']  / df_gr_dyn_city['cnt_order']
df_gr_dyn_city['offer2assign']  = df_gr_dyn_city['cnt_assign'] / df_gr_dyn_city['cnt_offer']
df_gr_dyn_city['assign2arrive'] = df_gr_dyn_city['cnt_arrive'] / df_gr_dyn_city['cnt_assign']
df_gr_dyn_city['arrive2trip']   = df_gr_dyn_city['cnt_trip']   / df_gr_dyn_city['cnt_arrive']
df_gr_dyn_city.head()

plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['cnt_order'], label = 'Казань')
plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['cnt_order'], label = 'Москва')
plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['cnt_order'], label = 'Санкт-Петербург')
plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['cnt_order'], label = 'Краснодар')
plt.legend()
plt.title("Количество заказов")
plt.show()

plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['order2trip'], label = 'Казань')
plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['order2trip'], label = 'Москва')
plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['order2trip'], label = 'Санкт-Петербург')
plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['order2trip'], label = 'Краснодар')
plt.legend()
plt.title("Order2Trip - Базовая конверсия")
plt.ylim([0,1])
plt.show()

plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['order2offer'], label = 'Казань')
plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['order2offer'], label = 'Москва')
plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['order2offer'], label = 'Санкт-Петербург')
plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['order2offer'], label = 'Краснодар')
plt.legend()
plt.title("Order2Offer - Конверсия из заказа в предложение")
plt.ylim([0,1])
plt.show()

plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['offer2assign'], label = 'Казань')
plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['offer2assign'], label = 'Москва')
plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['offer2assign'], label = 'Санкт-Петербург')
plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['offer2assign'], label = 'Краснодар')
plt.legend()
plt.title("Offer2Assign - Конверсия из предложения в назначение")
plt.ylim([0,1])
plt.show()

plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['assign2arrive'], label = 'Казань')
plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['assign2arrive'], label = 'Москва')
plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['assign2arrive'], label = 'Санкт-Петербург')
plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['assign2arrive'], label = 'Краснодар')
plt.legend()
plt.title("Assign2Arrive - Конверсия из назначения в прибытие")
plt.ylim([0,1])
plt.show()

plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['arrive2trip'], label = 'Казань')
plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['arrive2trip'], label = 'Москва')
plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['arrive2trip'], label = 'Санкт-Петербург')
plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['day_order'],
         df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['arrive2trip'], label = 'Краснодар')
plt.legend()
plt.title("Arrive2Trip - Конверсия из прибытия в завершение поездки")
plt.ylim([0,1])
plt.show()

